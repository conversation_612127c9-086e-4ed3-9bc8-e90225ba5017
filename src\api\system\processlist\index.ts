import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import { ContentTypeEnum } from '@/enums/httpEnum';
import { UserImportParam, ResetPwdParam, UserInfoResponse, DeptTree, User } from './model';

enum Api {
  deptTree = '/workflow/processDefinition/getNodeList',
  userList = '/system/user/listRefNode',
  identityLink = '/workflow/nodeIdentitylink',
}

export interface DeptTreeData {
  id: number;
  label: string;
  children?: DeptTreeData[];
}

export function userList(params: any) {
  return defHttp.get({ url: Api.userList, params });
}

export function identityChange(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.identityLink, data });
}

export function departmentTree() {
  return defHttp.get<DeptTree[]>({ url: Api.deptTree }, { ignoreCancelToken: true });
}
