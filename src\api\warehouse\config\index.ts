import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';

/** 查询仓库配置树形列表 */
export function multiLevelList(params) {
  return defHttp.get<any[]>({ url: '/basic/warehouse/tree', params });
}
/** 新增仓库配置 */
export function multiLevelAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: '/basic/warehouse/add', data });
}
/** 修改仓库配置 */
export function multiLevelUpdate(data: any) {
  return defHttp.postWithMsg<void>({ url: '/basic/warehouse/edit', data });
}
/** 删除仓库配置 */
export function multiLevelRemove(warehouseId) {
  return defHttp.get<void>({ url: '/basic/warehouse/delete/' + warehouseId });
}
/** 导出仓库配置 */
export function multiLevelExport(data: any) {
  return commonExport('/basic/warehouse/export', data);
}
/** 查询仓库选项列表 */
export function warehouseOption() {
  return defHttp.get<any[]>({ url: '/basic/warehouse/listSimple' });
}
/** 获取仓库详细信息 */
export function multiLevelInfo(warehouseId) {
  return defHttp.get({ url: '/basic/warehouse/' + warehouseId });
}
