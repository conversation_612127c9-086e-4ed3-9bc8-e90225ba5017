import { BaseEntity, PageQuery } from '@/api/base';

export interface TransferRequestOrderVO {
  /**
   * 主键
   */
  transferId: string | number;

  /**
   * 调拨申请单编号
   */
  transferCode: string;

  /**
   * 需求入库单id
   */
  demandEntryId: string | number;

  /**
   * 需求仓库id
   */
  demandWarehouseId: string | number;

  /**
   * 需求仓库库区
   */
  demandWarehouseZone: string;

  /**
   * 制单人
   */
  orderCreator: number;

  /**
   * 制单日期
   */
  orderCreateDate: string;

  /**
   * 经办人
   */
  operator: number;

  /**
   * 操作状态
   */
  optStatus: string;

  /**
   * 备注
   */
  remark: string;
}

export interface TransferRequestOrderForm extends BaseEntity {
  /**
   * 主键
   */
  transferId?: string | number;

  /**
   * 调拨申请单编号
   */
  transferCode?: string;

  /**
   * 需求入库单id
   */
  demandEntryId?: string | number;

  /**
   * 需求仓库id
   */
  demandWarehouseId?: string | number;

  /**
   * 需求仓库库区
   */
  demandWarehouseZone?: string;

  /**
   * 制单人
   */
  orderCreator?: number;

  /**
   * 制单日期
   */
  orderCreateDate?: string;

  /**
   * 经办人
   */
  operator?: number;

  /**
   * 操作状态
   */
  optStatus?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface TransferRequestOrderQuery extends PageQuery {
  /**
   * 调拨申请单编号
   */
  transferCode?: string;

  /**
   * 需求入库单id
   */
  demandEntryId?: string | number;

  /**
   * 需求仓库id
   */
  demandWarehouseId?: string | number;

  /**
   * 需求仓库库区
   */
  demandWarehouseZone?: string;

  /**
   * 制单人
   */
  orderCreator?: number;

  /**
   * 制单日期
   */
  orderCreateDate?: string;

  /**
   * 经办人
   */
  operator?: number;

  /**
   * 操作状态
   */
  optStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
