/**
 * 递归遍历树结构，并将 child 属性名换成 children
 * @param treeData 树形数据数组
 * @param callBack 回调函数
 * @param parentNode 父节点
 */
export function foreachTree(treeData: any[], callBack: Fn, parentNode = {}) {
  treeData.forEach((element) => {
    const newNode = callBack(element, parentNode) || element;
    
    // 如果存在 child 属性，将其重命名为 children
    if (element.child) {
      element.children = element.child;
      delete element.child;
      // 递归处理子节点
      foreachTree(element.children, callBack, newNode);
    }
  });
}

/**
 * 递归遍历树结构，将指定的子节点属性名换成 children
 * @param treeData 树形数据数组
 * @param childFieldName 子节点字段名（默认为 'child'）
 * @param callBack 回调函数（可选）
 * @param parentNode 父节点（可选）
 */
export function convertChildrenField(
  treeData: any[], 
  childFieldName = 'child', 
  callBack?: Fn, 
  parentNode = {}
) {
  treeData.forEach((element) => {
    // 执行回调函数（如果提供）
    const newNode = callBack ? (callBack(element, parentNode) || element) : element;
    
    // 如果存在指定的子节点属性，将其重命名为 children
    if (element[childFieldName]) {
      element.children = element[childFieldName];
      delete element[childFieldName];
      // 递归处理子节点
      convertChildrenField(element.children, childFieldName, callBack, newNode);
    }
  });
}

/**
 * 深度优先遍历树结构，支持自定义子节点字段名
 * @param treeData 树形数据数组
 * @param callBack 回调函数
 * @param childFieldName 子节点字段名（默认为 'children'）
 * @param parentNode 父节点（可选）
 */
export function traverseTree(
  treeData: any[], 
  callBack: Fn, 
  childFieldName = 'children', 
  parentNode = {}
) {
  treeData.forEach((element) => {
    const newNode = callBack(element, parentNode) || element;
    
    // 如果存在子节点，递归处理
    if (element[childFieldName] && Array.isArray(element[childFieldName])) {
      traverseTree(element[childFieldName], callBack, childFieldName, newNode);
    }
  });
}
