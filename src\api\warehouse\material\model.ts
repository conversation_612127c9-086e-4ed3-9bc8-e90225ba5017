import { BaseEntity, PageQuery } from '@/api/base';

export interface MaterialTypeVO {
  /**
   * 主键
   */
  typeId: string | number;

  /**
   * 类别编号
   */
  typeCode: string;

  /**
   * 类别名称
   */
  typeName: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 类别大类
   */
  typeCategory: string;
}

export interface MaterialTypeForm extends BaseEntity {
  /**
   * 主键
   */
  typeId?: string | number;

  /**
   * 类别编号
   */
  typeCode?: string;

  /**
   * 类别名称
   */
  typeName?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 类别大类
   */
  typeCategory?: string;
}

export interface MaterialTypeQuery extends PageQuery {
  /**
   * 类别编号
   */
  typeCode?: string;

  /**
   * 类别名称
   */
  typeName?: string;

  /**
   * 类别大类
   */
  typeCategory?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
