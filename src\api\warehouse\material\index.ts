import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import { MaterialTypeVO, MaterialTypeForm, MaterialTypeQuery } from './model';

/**
 * 查询物品类别列表
 * @param params
 * @returns
 */
export function materialTypeList(params?: MaterialTypeQuery) {
  return defHttp.get<MaterialTypeVO[]>({ url: '/basic/materialType/list', params });
}

/**
 * 导出物品类别列表
 * @param params
 * @returns
 */
export function materialTypeExport(params?: MaterialTypeQuery) {
  return commonExport('/basic/materialType/export', params ?? {});
}

/**
 * 查询物品类别详细
 * @param typeId id
 * @returns
 */
export function materialTypeInfo(typeId: ID) {
  return defHttp.get<MaterialTypeVO>({ url: '/basic/materialType/' + typeId });
}

/**
 * 新增物品类别
 * @param data
 * @returns
 */
export function materialTypeAdd(data: MaterialTypeForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/materialType/add', data });
}

/**
 * 更新物品类别
 * @param data
 * @returns
 */
export function materialTypeUpdate(data: MaterialTypeForm) {
  return defHttp.putWithMsg<void>({ url: '/basic/materialType/edit', data });
}

/**
 * 删除物品类别
 * @param typeId id
 * @returns
 */
export function materialTypeRemove(typeIds: ID | IDS) {
  return defHttp.get<void>({ url: '/basic/materialType/delete/' + typeIds });
}
