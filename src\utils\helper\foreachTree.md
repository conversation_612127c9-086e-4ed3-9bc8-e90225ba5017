# foreachTree 工具函数

这个模块提供了一系列用于处理树形数据结构的工具函数，特别是用于将 `child` 属性名转换为 `children` 的递归函数。

## 函数列表

### 1. foreachTree

递归遍历树结构，并将 `child` 属性名换成 `children`。

```typescript
function foreachTree(treeData: any[], callBack: Fn, parentNode = {})
```

**参数：**
- `treeData`: 树形数据数组
- `callBack`: 回调函数，对每个节点执行的操作
- `parentNode`: 父节点（可选，默认为空对象）

**示例：**
```typescript
import { foreachTree } from '@/utils/helper/foreachTree';

const data = [
  {
    id: 1,
    name: '部门1',
    child: [
      {
        id: 2,
        name: '子部门1',
        child: []
      }
    ]
  }
];

foreachTree(data, (item) => {
  item.icon = 'el:group'; // 添加图标
});

// 结果：child 属性被转换为 children
console.log(data[0].children); // 原来的 child 数组
```

### 2. convertChildrenField

递归遍历树结构，将指定的子节点属性名换成 `children`。

```typescript
function convertChildrenField(
  treeData: any[], 
  childFieldName = 'child', 
  callBack?: Fn, 
  parentNode = {}
)
```

**参数：**
- `treeData`: 树形数据数组
- `childFieldName`: 子节点字段名（默认为 'child'）
- `callBack`: 回调函数（可选）
- `parentNode`: 父节点（可选）

**示例：**
```typescript
import { convertChildrenField } from '@/utils/helper/foreachTree';

const data = [
  {
    id: 1,
    name: '节点1',
    items: [  // 自定义字段名
      {
        id: 2,
        name: '子节点1',
        items: []
      }
    ]
  }
];

convertChildrenField(data, 'items', (item) => {
  item.processed = true;
});

// 结果：items 属性被转换为 children
console.log(data[0].children);
```

### 3. traverseTree

深度优先遍历树结构，支持自定义子节点字段名。

```typescript
function traverseTree(
  treeData: any[], 
  callBack: Fn, 
  childFieldName = 'children', 
  parentNode = {}
)
```

**参数：**
- `treeData`: 树形数据数组
- `callBack`: 回调函数
- `childFieldName`: 子节点字段名（默认为 'children'）
- `parentNode`: 父节点（可选）

**示例：**
```typescript
import { traverseTree } from '@/utils/helper/foreachTree';

const data = [
  {
    id: 1,
    name: '根节点',
    children: [
      {
        id: 2,
        name: '子节点',
        children: []
      }
    ]
  }
];

traverseTree(data, (item, parent) => {
  item.level = parent.level ? parent.level + 1 : 0;
  item.fullPath = parent.fullPath ? `${parent.fullPath}/${item.name}` : item.name;
});
```

## 在 Vue 组件中的使用

### 部门树组件示例

```vue
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { foreachTree } from '@/utils/helper/foreachTree';
import { departmentTree } from '@/api/system/processlist';

const deptTreeArray = ref([]);

onMounted(async () => {
  const ret = await departmentTree();
  const treeData = Array.isArray(ret) ? ret : [ret];
  
  // 将 child 转换为 children，并添加图标
  foreachTree(treeData, (item) => {
    item.icon = 'el:group';
  });
  
  deptTreeArray.value = treeData;
});
</script>
```

## 常见用例

### 1. API 数据转换

当后端返回的树形数据使用 `child` 字段时：

```typescript
// 后端返回的数据格式
const apiData = {
  id: 1,
  name: '根部门',
  child: [
    {
      id: 2,
      name: '子部门',
      child: []
    }
  ]
};

// 转换为前端需要的格式
foreachTree([apiData], (item) => {
  // 可以在这里添加额外的处理逻辑
  item.key = item.id.toString();
  item.title = item.name;
});

// 现在 apiData.children 可用
```

### 2. 批量处理节点

```typescript
// 为所有节点添加统一属性
foreachTree(treeData, (item) => {
  item.selectable = true;
  item.checkable = true;
  item.icon = getIconByType(item.type);
});
```

### 3. 构建节点路径

```typescript
traverseTree(treeData, (item, parent) => {
  item.level = parent.level !== undefined ? parent.level + 1 : 0;
  item.parentPath = parent.fullPath || '';
  item.fullPath = item.parentPath ? `${item.parentPath}/${item.name}` : item.name;
});
```

## 注意事项

1. **原地修改**：这些函数会直接修改传入的数据对象，不会创建新的副本。
2. **类型安全**：建议在 TypeScript 项目中为数据定义适当的接口类型。
3. **性能考虑**：对于大型树结构，递归遍历可能会有性能影响。
4. **错误处理**：函数假设输入数据是有效的树结构，建议在使用前进行数据验证。

## 测试

运行测试：
```bash
npm run test src/utils/helper/__tests__/foreachTree.test.ts
```

查看示例：
```typescript
import { examples } from '@/utils/helper/foreachTree.example';

// 运行所有示例
examples.example1();
examples.example2();
examples.example3();
```
