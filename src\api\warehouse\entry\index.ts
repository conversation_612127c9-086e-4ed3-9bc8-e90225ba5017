import { defHttp } from '@/utils/http/axios';
import { commonExport } from '@/api/base';
import { WarehouseEntryOrderVO, WarehouseEntryOrderForm, WarehouseEntryOrderQuery } from '@/api/warehouse/entry/model';

/**
 * 查询入库单列表
 * @param params
 * @returns {*}
 */

export function warehouseEntryOrderList(params?: WarehouseEntryOrderQuery) {
  return defHttp.get<WarehouseEntryOrderVO[]>({ url: '/warehouse/entryOrder/list', params });
}
/**
 * 查询入库单详细
 * @param entryId
 */
export function warehouseEntryOrderInfo(entryId: string | number) {
  return defHttp.get<WarehouseEntryOrderVO>({ url: '/warehouse/entryOrder/' + entryId });
}

/**
 * 新增入库单
 * @param data
 */
export function warehouseEntryOrderAdd(data: WarehouseEntryOrderForm) {
  return defHttp.post<void>({ url: '/warehouse/entryOrder/save', data });
}
/**
 * 编辑入库单
 * @param data
 */
export function warehouseEntryOrderSubmit(data: WarehouseEntryOrderForm) {
  return defHttp.post<void>({ url: '/warehouse/entryOrder/submit', data });
}
/**
 * 结算入库单
 * @param data
 */
export function warehouseEntryOrderSettle(entryId: string | number | Array<string | number>) {
  return defHttp.get<void>({ url: '/warehouse/entryOrder/settle/' + entryId });
}

/**
 * 删除入库单
 * @param entryId
 */
export function warehouseEntryOrderRemove(entryId: string | number | Array<string | number>) {
  return defHttp.get<WarehouseEntryOrderVO[]>({ url: '/warehouse/entryOrder/delete/' + entryId });
}
/**
 * 批量删除入库单
 * @param entryId
 */
export function warehouseEntryOrderRemoveBatch(entryId: string | number | Array<string | number>) {
  return defHttp.get<WarehouseEntryOrderVO[]>({
    url: '/warehouse/entryOrder/deleteBatch/' + entryId,
  });
}
/**
 * 入库单列表导出
 * @param params
 * @returns {*}
 */
export function warehouseEntryOrderExport(params?: WarehouseEntryOrderQuery) {
  return commonExport('/warehouse/entryOrder/export', params ?? {});
}
export function invoiceOcr(data: any) {
  return defHttp.post<void>({ url: '/warehouse/ocr/invoice', data });
}
/**
 * 查询入库单待审核列表
 * @param params
 * @returns {*}
 */

export function warehouseEntryOrderListWait(params?: WarehouseEntryOrderQuery) {
  return defHttp.get<WarehouseEntryOrderVO[]>({ url: '/warehouse/entryOrder/listWait', params });
}
/**
 * 查询入库单审核完成列表
 * @param params
 * @returns {*}
 */

export function warehouseEntryOrderListFinsh(params?: WarehouseEntryOrderQuery) {
  return defHttp.get<WarehouseEntryOrderVO[]>({ url: '/warehouse/entryOrder/listFinish', params });
}
