import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import { AssetVO, AssetForm, AssetQuery } from './model';

/**
 * 查询台账管理-设备管理列表
 * @param params
 * @returns
 */
export function assetList(params?: AssetQuery) {
  return defHttp.get<AssetVO[]>({ url: '/basic/asset/list', params });
}

/**
 * 导出台账管理-设备管理列表
 * @param params
 * @returns
 */
export function assetExport(params?: AssetQuery) {
  return commonExport('/basic/asset/export', params ?? {});
}

/**
 * 查询台账管理-设备管理详细
 * @param assetId id
 * @returns
 */
export function assetInfo(assetId: ID) {
  return defHttp.get<AssetVO>({ url: '/basic/asset/' + assetId });
}

/**
 * 新增台账管理-设备管理
 * @param data
 * @returns
 */
export function assetAdd(data: AssetForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/asset/add', data });
}

/**
 * 更新台账管理-设备管理
 * @param data
 * @returns
 */
export function assetUpdate(data: AssetForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/asset/edit', data });
}

/**
 * 删除台账管理-设备管理
 * @returns
 * @param assetIds
 */
export function assetRemove(assetIds: ID | IDS) {
  return defHttp.get<void>({ url: '/basic/asset/delete/' + assetIds });
}

/** 获取设备导入信息模板 */
export function importTemplate() {
  return commonExport('/basic/asset/importTemplate');
}

/** 上传设备导入信息 */
export function uploadExcel(params, onUploadProgress) {
  return defHttp.uploadFile(
    {
      url: '/basic/asset/importData',
      onUploadProgress,
    },
    params,
    true,
  );
}
