import { defHttp } from '@/utils/http/axios';
import { ID, commonExport } from '@/api/base';
import { WarehouseStockVO, WarehouseStockQuery } from './model';

/**
 * 查询库存管理列表
 * @param params
 * @returns
 */
export function warehouseStockList(params?: WarehouseStockQuery) {
  return defHttp.get<WarehouseStockVO[]>({ url: '/basic/warehouseStock/list', params });
}

/**
 * 导出库存管理列表
 * @param params
 * @returns
 */
export function warehouseStockExport(params?: WarehouseStockQuery) {
  return commonExport('/basic/warehouseStock/export', params ?? {});
}

/**
 * 查询库存管理详细
 * @param stockId id
 * @returns
 */
export function warehouseStockInfo(stockId: ID) {
  return defHttp.get<WarehouseStockVO>({ url: '/basic/warehouseStock/' + stockId });
}
