import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import {
  TransferRequestOrderVO,
  TransferRequestOrderForm,
  TransferRequestOrderQuery,
} from './model';

/**
 * 查询调拨申请单列表
 * @param params
 * @returns
 */
export function transferRequestOrderList(params?: TransferRequestOrderQuery) {
  return defHttp.get<TransferRequestOrderVO[]>({
    url: '/warehouse/transfer/list',
    params,
  });
}

/**
 * 导出调拨申请单列表
 * @param params
 * @returns
 */
export function transferRequestOrderExport(params?: TransferRequestOrderQuery) {
  return commonExport('/warehouse/transfer/export', params ?? {});
}

/**
 * 查询调拨申请单详细
 * @param transferId id
 * @returns
 */
export function transferRequestOrderInfo(transferId: ID) {
  return defHttp.get<TransferRequestOrderVO>({ url: '/warehouse/transfer/' + transferId });
}

/**
 * 提交调拨申请单
 * @param data
 * @returns
 */
export function transferRequestOrderSubmit(data: TransferRequestOrderForm) {
  return defHttp.postWithMsg<void>({ url: '/warehouse/transfer/submit', data });
}

/**
 * 保存调拨申请单
 * @param data
 * @returns
 */
export function transferRequestOrderSave(data: TransferRequestOrderForm) {
  return defHttp.postWithMsg<void>({ url: '/warehouse/transfer/save', data });
}

/**
 * 更新调拨申请单
 * @param data
 * @returns
 */
export function transferRequestOrderUpdate(data: TransferRequestOrderForm) {
  return defHttp.putWithMsg<void>({ url: '/system/transferRequestOrder', data });
}

/**
 * 删除调拨申请单
 * @param transferId id
 * @returns
 */
export function transferRequestOrderRemove(transferId: ID | IDS) {
  return defHttp.get<void>({ url: '/warehouse/transfer/delete/' + transferId });
}

/**
 * 批量删除调拨申请单
 * @param transferIds ids
 * @returns
 */
export function transferRequestOrderBatchRemove(transferIds: ID | IDS) {
  return defHttp.get<void>({ url: '/warehouse/transfer/deleteBatch/' + transferIds });
}
