import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import {
  RepairOrderMaterialRequestVO,
  RepairOrderMaterialRequestForm,
  RepairOrderMaterialRequestQuery,
} from './model';

/**
 * 查询维修工单列表
 * @param params
 * @returns
 */
export function repairOrderMaterialRequestList(params?: RepairOrderMaterialRequestQuery) {
  return defHttp.get<RepairOrderMaterialRequestVO[]>({
    url: '/basic/repairOrderMaterialRequest/listOrder',
    params,
  });
}

/**
 * 查询维修工单备件申请列表
 * @param params
 * @returns
 */
export function repairOrderMaterialRequest(params?: RepairOrderMaterialRequestQuery) {
  return defHttp.get<RepairOrderMaterialRequestVO[]>({
    url: '/basic/repairOrderMaterialRequest/list',
    params,
  });
}

/**
 * 备件申请
 * @param data
 * @returns
 */
// export function materialRequestAdd(data: RepairOrderMaterialRequestForm) {
//   return defHttp.postWithMsg<void>({ url: '/basic/repairOrderMaterialRequestMain/add', data });
// }

/**
 * 二级库主库处置
 * @param data
 * @returns
 */
export function materialRequestMain(data: RepairOrderMaterialRequestForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/repairOrderMaterialRequestMain/edit', data });
}

/**
 * 详情接口
 * @param params
 * @returns
 */
export function materialRequestInfo(materialRequestId: ID) {
  return defHttp.get<RepairOrderMaterialRequestVO>({
    url: '/basic/repairOrderMaterialRequestMain/' + materialRequestId,
  });
}
/**
 * 详情接口
 * @param params
 * @returns
 */
export function materialAndOrder(orderId,materialId) {
  return defHttp.get({
    url: '/basic/repairOrderMaterialRequestMain/materialAndOrder' ,
    params: {
      "materialId":materialId,
      "orderId":orderId
    }
  });
}

/**
 * 新增配件
 * @param data
 * @returns
 */
// export function materialRequestAd(data: RepairOrderMaterialRequestForm) {
//   return defHttp.postWithMsg<void>({ url: '/basic/repairOrderMaterialRequest/add', data });
// }

/**
 * 修改配件
 * @param data
 * @returns
 */
export function materialRequestEdit(data: RepairOrderMaterialRequestForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/repairOrderMaterialRequest/edit', data });
}

/**
 * 删除配件
 * @param id id
 * @returns
 */
export function materialRequestDel(id: ID | IDS) {
  return defHttp.get<void>({ url: '/basic/repairOrderMaterialRequest/delete/' + id });
}

/**
 * 导出维修工单备件申请列表
 * @param params
 * @returns
 */
export function repairOrderMaterialRequestExport(params?: RepairOrderMaterialRequestQuery) {
  return commonExport('/basic/repairOrderMaterialRequest/export', params ?? {});
}
