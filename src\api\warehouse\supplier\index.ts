import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import { SupplierVO, SupplierForm, SupplierQuery } from './model';

/**
 * 查询供应商列表
 * @param params
 * @returns
 */
export function supplierList(params?: SupplierQuery) {
  return defHttp.get<SupplierVO[]>({ url: '/basic/supplier/list', params });
}

/**
 * 导出供应商列表
 * @param params
 * @returns
 */
export function supplierExport(params?: SupplierQuery) {
  return commonExport('/basic/supplier/export', params ?? {});
}

/**
 * 查询供应商详细
 * @param supplierId id
 * @returns
 */
export function supplierInfo(supplierId: ID) {
  return defHttp.get<SupplierVO>({ url: '/basic/supplier/' + supplierId });
}

/**
 * 新增供应商
 * @param data
 * @returns
 */
export function supplierAdd(data: SupplierForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/supplier/add', data });
}

/**
 * 更新供应商
 * @param data
 * @returns
 */
export function supplierUpdate(data: SupplierForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/supplier/edit', data });
}

/**
 * 删除供应商
 * @param supplierId id
 * @returns
 */
export function supplierRemove(supplierId: ID | IDS) {
  return defHttp.get<void>({ url: '/basic/supplier/delete/' + supplierId });
}

/**

 * 查询所有供应商列表

 * @param params

 * @returns

 */

export function supplierAllList() {
  return defHttp.get<SupplierVO[]>({ url: '/basic/supplier/listSimple' });
}

/**

 * 查询所有物品列表

 * @param params

 * @returns

 */

export function materialAllList() {
  return defHttp.get<SupplierVO[]>({ url: '/basic/material/listSimple' });
}

/**

 * 查询所有项目列表

 * @param params

 * @returns

 */

export function projectAllList() {
  return defHttp.get<SupplierVO[]>({ url: '/basic/project/listSimple' });
}
