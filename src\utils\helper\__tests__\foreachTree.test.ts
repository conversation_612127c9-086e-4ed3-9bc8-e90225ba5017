/**
 * foreachTree 函数测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { foreachTree, convertChildrenField, traverseTree } from '../foreachTree';

describe('foreachTree', () => {
  let testData: any[];

  beforeEach(() => {
    testData = [
      {
        id: 1,
        name: 'root1',
        child: [
          {
            id: 2,
            name: 'child1-1',
            child: [
              {
                id: 3,
                name: 'child1-1-1',
                child: []
              }
            ]
          },
          {
            id: 4,
            name: 'child1-2',
            child: []
          }
        ]
      },
      {
        id: 5,
        name: 'root2',
        child: [
          {
            id: 6,
            name: 'child2-1',
            child: []
          }
        ]
      }
    ];
  });

  it('should convert child to children recursively', () => {
    foreachTree(testData, () => {});

    // 检查根节点
    expect(testData[0]).toHaveProperty('children');
    expect(testData[0]).not.toHaveProperty('child');
    expect(testData[1]).toHaveProperty('children');
    expect(testData[1]).not.toHaveProperty('child');

    // 检查子节点
    expect(testData[0].children[0]).toHaveProperty('children');
    expect(testData[0].children[0]).not.toHaveProperty('child');
    expect(testData[0].children[1]).toHaveProperty('children');
    expect(testData[0].children[1]).not.toHaveProperty('child');

    // 检查深层子节点
    expect(testData[0].children[0].children[0]).toHaveProperty('children');
    expect(testData[0].children[0].children[0]).not.toHaveProperty('child');
  });

  it('should execute callback for each node', () => {
    const processedNodes: string[] = [];

    foreachTree(testData, (item: any) => {
      processedNodes.push(item.name);
      item.processed = true;
    });

    // 检查所有节点都被处理了
    expect(processedNodes).toContain('root1');
    expect(processedNodes).toContain('root2');
    expect(processedNodes).toContain('child1-1');
    expect(processedNodes).toContain('child1-2');
    expect(processedNodes).toContain('child1-1-1');
    expect(processedNodes).toContain('child2-1');

    // 检查所有节点都有 processed 标记
    expect(testData[0].processed).toBe(true);
    expect(testData[0].children[0].processed).toBe(true);
    expect(testData[0].children[0].children[0].processed).toBe(true);
  });

  it('should handle empty child arrays', () => {
    const dataWithEmptyChild = [
      {
        id: 1,
        name: 'root',
        child: []
      }
    ];

    foreachTree(dataWithEmptyChild, () => {});

    expect(dataWithEmptyChild[0]).toHaveProperty('children');
    expect(dataWithEmptyChild[0].children).toEqual([]);
    expect(dataWithEmptyChild[0]).not.toHaveProperty('child');
  });

  it('should handle nodes without child property', () => {
    const dataWithoutChild = [
      {
        id: 1,
        name: 'root'
      }
    ];

    foreachTree(dataWithoutChild, (item: any) => {
      item.processed = true;
    });

    expect(dataWithoutChild[0].processed).toBe(true);
    expect(dataWithoutChild[0]).not.toHaveProperty('children');
  });
});

describe('convertChildrenField', () => {
  let testData: any[];

  beforeEach(() => {
    testData = [
      {
        id: 1,
        name: 'root1',
        items: [
          {
            id: 2,
            name: 'child1-1',
            items: []
          }
        ]
      }
    ];
  });

  it('should convert custom field name to children', () => {
    convertChildrenField(testData, 'items');

    expect(testData[0]).toHaveProperty('children');
    expect(testData[0]).not.toHaveProperty('items');
    expect(testData[0].children[0]).toHaveProperty('children');
    expect(testData[0].children[0]).not.toHaveProperty('items');
  });

  it('should execute callback if provided', () => {
    const processedNodes: string[] = [];

    convertChildrenField(testData, 'items', (item: any) => {
      processedNodes.push(item.name);
      item.processed = true;
    });

    expect(processedNodes).toContain('root1');
    expect(processedNodes).toContain('child1-1');
    expect(testData[0].processed).toBe(true);
    expect(testData[0].children[0].processed).toBe(true);
  });
});

describe('traverseTree', () => {
  let testData: any[];

  beforeEach(() => {
    testData = [
      {
        id: 1,
        name: 'root1',
        children: [
          {
            id: 2,
            name: 'child1-1',
            children: [
              {
                id: 3,
                name: 'child1-1-1',
                children: []
              }
            ]
          }
        ]
      }
    ];
  });

  it('should traverse tree with children field', () => {
    const processedNodes: string[] = [];

    traverseTree(testData, (item: any) => {
      processedNodes.push(item.name);
      item.visited = true;
    });

    expect(processedNodes).toContain('root1');
    expect(processedNodes).toContain('child1-1');
    expect(processedNodes).toContain('child1-1-1');

    expect(testData[0].visited).toBe(true);
    expect(testData[0].children[0].visited).toBe(true);
    expect(testData[0].children[0].children[0].visited).toBe(true);
  });

  it('should work with custom children field name', () => {
    const customData = [
      {
        id: 1,
        name: 'root',
        items: [
          {
            id: 2,
            name: 'child',
            items: []
          }
        ]
      }
    ];

    const processedNodes: string[] = [];

    traverseTree(customData, (item: any) => {
      processedNodes.push(item.name);
    }, 'items');

    expect(processedNodes).toContain('root');
    expect(processedNodes).toContain('child');
  });
});
