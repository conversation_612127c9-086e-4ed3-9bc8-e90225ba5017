/**
 * 简单的 JavaScript 测试文件
 */

function foreachTree(treeData, callBack, parentNode = {}) {
  treeData.forEach((element) => {
    const newNode = callBack(element, parentNode) || element;
    
    // 如果存在 child 属性，将其重命名为 children
    if (element.child) {
      element.children = element.child;
      delete element.child;
      // 递归处理子节点
      foreachTree(element.children, callBack, newNode);
    }
  });
}

// 测试数据
const testData = [
  {
    id: 1,
    name: 'root',
    child: [
      {
        id: 2,
        name: 'child1',
        child: [
          {
            id: 3,
            name: 'grandchild1',
            child: []
          }
        ]
      },
      {
        id: 4,
        name: 'child2',
        child: []
      }
    ]
  }
];

console.log('=== 测试 foreachTree 函数 ===');
console.log('处理前的数据:');
console.log(JSON.stringify(testData, null, 2));

// 执行转换
foreachTree(testData, (item) => {
  item.processed = true;
  item.icon = 'el:group';
  console.log(`处理节点: ${item.name}`);
});

console.log('\n处理后的数据:');
console.log(JSON.stringify(testData, null, 2));

// 验证结果
function validateResult(data, level = 0) {
  const indent = '  '.repeat(level);
  data.forEach(item => {
    console.log(`${indent}✓ 节点 ${item.name}:`);
    console.log(`${indent}  - 有 processed 属性: ${item.processed === true}`);
    console.log(`${indent}  - 有 icon 属性: ${item.icon === 'el:group'}`);
    console.log(`${indent}  - 没有 child 属性: ${!item.hasOwnProperty('child')}`);
    
    if (item.children && item.children.length > 0) {
      console.log(`${indent}  - 有 children 属性，包含 ${item.children.length} 个子节点`);
      validateResult(item.children, level + 1);
    } else if (item.children) {
      console.log(`${indent}  - 有 children 属性（空数组）`);
    }
  });
}

console.log('\n=== 验证结果 ===');
validateResult(testData);

console.log('\n=== 测试完成 ===');
console.log('✅ foreachTree 函数工作正常！');
