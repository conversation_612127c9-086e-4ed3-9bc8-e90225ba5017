import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';
import { MaterialVO, MaterialForm, MaterialQuery } from './model';

/**
 * 查询物品列表
 * @param params
 * @returns
 */
export function materialList(params?: MaterialQuery) {
  return defHttp.get<MaterialVO[]>({ url: '/basic/material/list', params });
}

/**
 * 导出物品列表
 * @param params
 * @returns
 */
export function materialExport(params?: MaterialQuery) {
  return commonExport('/basic/material/export', params ?? {});
}

/**
 * 查询物品详细
 * @param materialId id
 * @returns
 */
export function materialInfo(materialId: ID) {
  return defHttp.get<MaterialVO>({ url: '/basic/material/' + materialId });
}

/**
 * 新增物品
 * @param data
 * @returns
 */
export function materialAdd(data: MaterialForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/material/add', data });
}

/**
 * 更新物品
 * @param data
 * @returns
 */
export function materialUpdate(data: MaterialForm) {
  return defHttp.postWithMsg<void>({ url: '/basic/material/edit', data });
}

/**
 * 删除物品
 * @param materialId id
 * @returns
 */
export function materialRemove(materialId: ID | IDS) {
  return defHttp.get<void>({ url: '/basic/material/delete/' + materialId });
}
