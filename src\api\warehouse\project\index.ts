import { defHttp } from '@/utils/http/axios';
import { ID, IDS, commonExport } from '@/api/base';

/** 查询项目管理列表 */
export function listSimple(params) {
  return defHttp.get({ url: '/basic/project/list', params });
}
/** 导出项目管理列表 */
export function projectExport(params) {
  return commonExport('/basic/project/export', params ?? {});
}
/** 新增项目管理条目 */
export function projectAdd(data) {
  return defHttp.postWithMsg<void>({ url: '/basic/project/add', data });
}
/** 编辑项目管理条目 */
export function projectUpdate(data) {
  return defHttp.postWithMsg<void>({ url: '/basic/project/edit', data });
}
/** 查询项目管理条目详情 */
export function projectInfo(projectId) {
  return defHttp.get({ url: '/basic/project/' + projectId });
}
/** 删除项目管理条目 */
export function projectRemove(projectId) {
  return defHttp.get<void>({ url: '/basic/project/delete/' + projectId });
}
