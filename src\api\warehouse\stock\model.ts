import { BaseEntity, PageQuery } from '@/api/base';

export interface WarehouseStockVO {
  /**
   * 主键
   */
  stockId: string | number;

  /**
   * 物品类别id
   */
  typeId: string | number;

  /**
   * 物品id
   */
  materialId: string | number;

  /**
   * 物品编号
   */
  materialCode: string;

  /**
   * 物品名称
   */
  materialName: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 规格型号
   */
  specification: string;

  /**
   * 是否耐用
   */
  isDurable: string;

  /**
   * 仓库
   */
  warehouse: string;

  /**
   * 库区
   */
  warehouseZone: string;

  /**
   * 库存数量
   */
  stockCount: string;

  /**
   * 单价
   */
  unitPrice: number;

  /**
   * 总价
   */
  totalPrice: number;

  /**
   * 品质
   */
  quality: string;

  /**
   * 预警上限
   */
  warnUpperLimit: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit: string | number;
}

export interface WarehouseStockForm extends BaseEntity {
  /**
   * 主键
   */
  stockId?: string | number;

  /**
   * 物品类别id
   */
  typeId?: string | number;

  /**
   * 物品id
   */
  materialId?: string | number;

  /**
   * 物品编号
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 是否耐用
   */
  isDurable?: string;

  /**
   * 仓库
   */
  warehouse?: string;

  /**
   * 库区
   */
  warehouseZone?: string;

  /**
   * 库存数量
   */
  stockCount?: string;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 总价
   */
  totalPrice?: number;

  /**
   * 品质
   */
  quality?: string;

  /**
   * 预警上限
   */
  warnUpperLimit?: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit?: string | number;
}

export interface WarehouseStockQuery extends PageQuery {
  /**
   * 物品类别id
   */
  typeId?: string | number;

  /**
   * 物品id
   */
  materialId?: string | number;

  /**
   * 物品编号
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 是否耐用
   */
  isDurable?: string;

  /**
   * 仓库
   */
  warehouse?: string;

  /**
   * 库区
   */
  warehouseZone?: string;

  /**
   * 库存数量
   */
  stockCount?: string;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 总价
   */
  totalPrice?: number;

  /**
   * 品质
   */
  quality?: string;

  /**
   * 预警上限
   */
  warnUpperLimit?: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
