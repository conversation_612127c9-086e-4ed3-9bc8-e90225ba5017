import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { multiLevelDictModel, typeof_maintenanceAttributeMap } from './model';
// import { Result } from '/#/axios';

enum Api {
  root = '/basic/multiLevelDict/',
  multiLevelList = root + 'list',
  multiLevelAdd = root + 'add',
  multiLevelUpdate = root + 'edit',
  multiLevelRemove = root + 'delete',
  multiLevelExport = root + 'export',
  multiLevelNodeList = root + 'list/exclude',
}

export function multiLevelList(params?: any) {
  return defHttp.get<any[]>({ url: Api.multiLevelList, params });
}

export function multiLevelInfo(multiDictId: ID) {
  return defHttp.get<multiLevelDictModel>({ url: Api.root + '/' + multiDictId });
}

export function multiLevelExport(data: any) {
  return commonExport(Api.multiLevelExport, data);
}

export function multiLevelUpdate(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.multiLevelUpdate, data });
}

export function multiLevelAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.multiLevelAdd, data });
}

export function multiLevelRemove(multiDictId: IDS) {
  return defHttp.postWithMsg<void>({ url: Api.multiLevelRemove + '/' + multiDictId });
}

export function multiLevelNodeList(multiDictId: ID, multiDictType: ID) {
  return defHttp.get<any[]>({
    url: Api.multiLevelNodeList + '/' + multiDictId + '/' + multiDictType,
  });
}

/** 查询所有站段下所有车站信息 */
export function allStation() {
  return defHttp.get<any>({
    url: Api.multiLevelList + '/allStation',
  });
}
/** 根据设备id获取设备维护属性信息 */
export function getMaintenanceAttribute(assetCategoryId: string) {
  return defHttp.get<typeof_maintenanceAttributeMap[]>({
    url: Api.root + 'getMaintenanceAttribute/' + assetCategoryId,
  });
}

/** 线路关联车站 */
export function addLineStation(data) {
  return defHttp.postWithMsg({
    url: Api.root + 'addLineStation',
    data,
  });
}

/** 获取线路 */
export function getSelAllLine() {
  return defHttp.get({ url: '/basic/multiLevelDict/selAllLine' });
}

/** 获取车站 */
export function getSelAllLineStation(params?) {
  return defHttp.get({ url: '/basic/multiLevelDict/selAllLineStation', params });
}
