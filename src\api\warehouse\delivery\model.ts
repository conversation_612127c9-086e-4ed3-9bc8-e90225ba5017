import { BaseEntity, PageQuery } from '@/api/base';

interface ItemData {
  key: string;
  materialCode: string;
  materialId: string;
  warehouseArea: string;
  isDurable: boolean;
  specification: string;
  unit: string;
  entryCount: number;
  entryUnitPrice: number;
  entryTotalPrice: number;
  quality: string;
  expireDate: string;
  remark: string;
}
export interface WarehouseDeliveryOrderVO {
  /**
   * 主键
   */
  deliveryId: string | number;

  /**
   * 出库单编号
   */
  deliveryCode: string;

  /**
   * 出库类型
   */
  deliveryType: string;

  /**
   * 供应商id
   */
  supplierId: string | number;

  /**
   * 供应商名称
   */
  supplierName: string;

  /**
   * 出库仓库
   */
  deliveryWarehouse: string;

  /**
   * 项目
   */
  projectId: string | number;

  /**
   * 经办人
   */
  operator: string;

  /**
   * 制单人
   */
  orderCreator: string;

  /**
   * 制单日期
   */
  orderCreateDate: string;

  /**
   * 发票号
   */
  invoiceNumber: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 附件列表
   */
  attachments: string;

  /**
   * 审核状态
   */
  approvalStatus: string;

  /**
   * 审核人
   */
  approver: string;

  /**
   * 审核日期
   */
  approvalDate: string;

  /**
   * 审核备注
   */
  approvalRemark: string;
  entryMaterialList: Array<ItemData>;
  optStatus: string;
}

export interface WarehouseDeliveryOrderForm extends BaseEntity {
  /**
   * 主键
   */
  deliveryId?: string | number;

  /**
   * 出库单编号
   */
  deliveryCode?: string;

  /**
   * 出库类型
   */
  deliveryType?: string;

  /**
   * 供应商id
   */
  supplierId?: string | number;

  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 出库仓库
   */
  deliveryWarehouse?: string;

  /**
   * 项目
   */
  projectId?: string | number;

  /**
   * 经办人
   */
  operator?: string;

  /**
   * 制单人
   */
  orderCreator?: string;

  /**
   * 制单日期
   */
  orderCreateDate?: string;

  /**
   * 发票号
   */
  invoiceNumber?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 附件列表
   */
  attachments?: string;

  /**
   * 审核状态
   */
  approvalStatus?: string;

  /**
   * 审核人
   */
  approver?: string;

  /**
   * 审核日期
   */
  approvalDate?: string;

  /**
   * 审核备注
   */
  approvalRemark?: string;

}

export interface WarehouseDeliveryOrderQuery extends PageQuery {

  /**
   * 出库单编号
   */
  deliveryCode?: string;

  /**
   * 出库类型
   */
  deliveryType?: string;

  /**
   * 供应商id
   */
  supplierId?: string | number;

  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 出库仓库
   */
  deliveryWarehouse?: string;

  /**
   * 项目
   */
  projectId?: string | number;

  /**
   * 经办人
   */
  operator?: string;

  /**
   * 制单人
   */
  orderCreator?: string;

  /**
   * 制单日期
   */
  orderCreateDate?: string;

  /**
   * 发票号
   */
  invoiceNumber?: string;

  /**
   * 附件列表
   */
  attachments?: string;

  /**
   * 审核状态
   */
  approvalStatus?: string;

  /**
   * 审核人
   */
  approver?: string;

  /**
   * 审核日期
   */
  approvalDate?: string;

  /**
   * 审核备注
   */
  approvalRemark?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
