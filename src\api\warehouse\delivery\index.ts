/**
 * 仓库出库单管理API
 * 提供出库单的增删改查、提交、结算等功能接口
 */
import { defHttp } from '@/utils/http/axios';
import { commonExport } from '@/api/base';
import {
  WarehouseDeliveryOrderVO,
  WarehouseDeliveryOrderForm,
  WarehouseDeliveryOrderQuery,
} from '@/api/warehouse/delivery/model';

/**
 * 获取出库单列表
 * @param params 查询参数
 * @returns 出库单列表数据
 */
export function warehouseDeliveryOrderList(params?: WarehouseDeliveryOrderQuery) {
  return defHttp.get<WarehouseDeliveryOrderVO[]>({ url: '/warehouse/deliveryOrder/list', params });
}

/**
 * 根据ID获取出库单详情
 * @param deliveryId 出库单ID
 * @returns 出库单详细信息
 */
export function warehouseDeliveryOrderInfo(deliveryId: string | number) {
  return defHttp.get<WarehouseDeliveryOrderVO>({ url: '/warehouse/deliveryOrder/' + deliveryId });
}

/**
 * 新增出库单
 * @param data 出库单表单数据
 * @returns 无返回值
 */
export function warehouseDeliveryOrderAdd(data: WarehouseDeliveryOrderForm) {
  return defHttp.post<void>({ url: '/warehouse/deliveryOrder/save', data });
}

/**
 * 提交出库单
 * @param data 出库单表单数据
 * @returns 无返回值
 */
export function warehouseDeliveryOrderSubmit(data: WarehouseDeliveryOrderForm) {
  return defHttp.post<void>({ url: '/warehouse/deliveryOrder/submit', data });
}

/**
 * 结算出库单
 * @param deliveryId 出库单ID，支持单个ID或ID数组
 * @returns 无返回值
 */
export function warehouseDeliveryOrderSettle(deliveryId: string | number | Array<string | number>) {
  return defHttp.get<void>({ url: '/warehouse/deliveryOrder/settle/' + deliveryId });
}

/**
 * 删除出库单
 * @param deliveryId 出库单ID，支持单个ID或ID数组
 * @returns 被删除的出库单数据
 */
export function warehouseDeliveryOrderRemove(deliveryId: string | number | Array<string | number>) {
  return defHttp.get<WarehouseDeliveryOrderVO[]>({
    url: '/warehouse/deliveryOrder/delete/' + deliveryId,
  });
}

/**
 * 批量删除出库单
 * @param deliveryId 出库单ID数组
 * @returns 被删除的出库单数据
 */
export function warehouseDeliveryOrderRemoveBatch(
  deliveryId: string | number | Array<string | number>,
) {
  return defHttp.get<WarehouseDeliveryOrderVO[]>({
    url: '/warehouse/deliveryOrder/deleteBatch/' + deliveryId,
  });
}

/**
 * 导出出库单数据
 * @param params 查询参数
 * @returns 导出的文件流
 */
export function warehouseDeliveryOrderExport(params?: WarehouseDeliveryOrderQuery) {
  return commonExport('/warehouse/deliveryOrder/export', params ?? {});
}

/**
 * 获取待出库单列表
 * @param params 查询参数
 * @returns 待出库单列表数据
 */
export function warehouseDeliveryOrderListWait(params?: WarehouseDeliveryOrderQuery) {
  return defHttp.get<WarehouseDeliveryOrderVO[]>({
    url: '/warehouse/deliveryOrder/listWait',
    params,
  });
}

/**
 * 获取已完成出库单列表
 * @param params 查询参数
 * @returns 已完成出库单列表数据
 */
export function warehouseDeliveryOrderListFinsh(params?: WarehouseDeliveryOrderQuery) {
  return defHttp.get<WarehouseDeliveryOrderVO[]>({
    url: '/warehouse/deliveryOrder/listFinish',
    params,
  });
}
