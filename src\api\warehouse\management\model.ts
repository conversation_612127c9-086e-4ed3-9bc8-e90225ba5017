import { BaseEntity, PageQuery } from '@/api/base';

export interface MaterialVO {
  /**
   * 主键
   */
  materialId: string | number;

  /**
   * 物品编码
   */
  materialCode: string;

  /**
   * 物品名称
   */
  materialName: string;

  /**
   * 物品序列号
   */
  materialSerialNumber: string;

  /**
   * 类别id
   */
  typeId: string | number;

  /**
   * 预警上限
   */
  warnUpperLimit: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit: string | number;

  /**
   * 单位
   */
  unit: string;

  /**
   * 是否耐用品
   */
  isDurable: string;

  /**
   * 是否返修件
   */
  isRepaired: string;

  /**
   * 规格型号
   */
  specification: string;

  /**
   * 颜色
   */
  color: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 主要供货商id
   */
  mainSupplierId: string | number;

  /**
   * 描述
   */
  description: string;

  /**
   * 备注
   */
  remark: string;
}

export interface MaterialForm extends BaseEntity {
  /**
   * 主键
   */
  materialId?: string | number;

  /**
   * 物品编码
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 物品序列号
   */
  materialSerialNumber?: string;

  /**
   * 类别id
   */
  typeId?: string | number;

  /**
   * 预警上限
   */
  warnUpperLimit?: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit?: string | number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 是否耐用品
   */
  isDurable?: string;

  /**
   * 是否返修件
   */
  isRepaired?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 颜色
   */
  color?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 主要供货商id
   */
  mainSupplierId?: string | number;

  /**
   * 描述
   */
  description?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface MaterialQuery extends PageQuery {
  /**
   * 物品编码
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 物品序列号
   */
  materialSerialNumber?: string;

  /**
   * 类别id
   */
  typeId?: string | number;

  /**
   * 预警上限
   */
  warnUpperLimit?: string | number;

  /**
   * 预警下限
   */
  warnLowerLimit?: string | number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 是否耐用品
   */
  isDurable?: string;

  /**
   * 是否返修件
   */
  isRepaired?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 颜色
   */
  color?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 主要供货商id
   */
  mainSupplierId?: string | number;

  /**
   * 描述
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
