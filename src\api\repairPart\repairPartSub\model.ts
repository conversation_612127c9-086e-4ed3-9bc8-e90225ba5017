import { BaseEntity, PageQuery } from '@/api/base';

export interface RepairOrderMaterialRequestVO {
  /**
   *
   */
  id: string | number;

  /**
   * 关联维修工单ID
   */
  orderId: string | number;

  /**
   * 工单号
   */
  orderNo: string;

  /**
   * 关联备件ID
   */
  materialId: string | number;

  /**
   * 物品类别名称
   */
  typeName: string;

  /**
   * 物品编号
   */
  materialCode: string;

  /**
   * 物品名称
   */
  materialName: string;

  /**
   * 规格型号
   */
  specification: string;

  /**
   * 单位
   */
  unit: string;

  /**
   * 申请数量
   */
  quantity: number;

  /**
   * 是否库存中（由触发器更新）
   */
  isInStock: string;

  /**
   *
   */
  status: string;

  /**
   * 操作类型
   */
  actionType: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 删除人
   */
  deleteBy: number;

  /**
   * 删除时间
   */
  deleteTime: string;
}

export interface RepairOrderMaterialRequestForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 关联维修工单ID
   */
  orderId?: string | number;

  /**
   * 工单号
   */
  orderNo?: string;

  /**
   * 关联备件ID
   */
  materialId?: string | number;

  /**
   * 物品类别名称
   */
  typeName?: string;

  /**
   * 物品编号
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 申请数量
   */
  quantity?: number;

  /**
   * 是否库存中（由触发器更新）
   */
  isInStock?: string;

  /**
   *
   */
  status?: string;

  /**
   * 操作类型
   */
  actionType?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 删除人
   */
  deleteBy?: number;

  /**
   * 删除时间
   */
  deleteTime?: string;
}

export interface RepairOrderMaterialRequestQuery extends PageQuery {
  /**
   * 关联维修工单ID
   */
  orderId?: string | number;

  /**
   * 工单号
   */
  orderNo?: string;

  /**
   * 关联备件ID
   */
  materialId?: string | number;

  /**
   * 物品类别名称
   */
  typeName?: string;

  /**
   * 物品编号
   */
  materialCode?: string;

  /**
   * 物品名称
   */
  materialName?: string;

  /**
   * 规格型号
   */
  specification?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 申请数量
   */
  quantity?: number;

  /**
   * 是否库存中（由触发器更新）
   */
  isInStock?: string;

  /**
   *
   */
  status?: string;

  /**
   * 操作类型
   */
  actionType?: string;

  /**
   * 删除人
   */
  deleteBy?: number;

  /**
   * 删除时间
   */
  deleteTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
