import { BaseEntity, PageQuery } from '@/api/base';

export interface SupplierVO {
  /**
   * 主键
   */
  supplierId: string | number;

  /**
   * 供应商编号
   */
  supplierCode: string;

  /**
   * 供应商名称
   */
  supplierName: string;

  /**
   * 供应商地址
   */
  supplierAddr: string;

  /**
   * 联系人
   */
  contact: string;

  /**
   * 电话
   */
  phone: string;

  /**
   * 电子邮件
   */
  email: string;

  /**
   * 纳税人识别号
   */
  taxPayerNumber: string;

  /**
   * 备注
   */
  remark: string;
}

export interface SupplierForm extends BaseEntity {
  /**
   * 主键
   */
  supplierId?: string | number;

  /**
   * 供应商编号
   */
  supplierCode?: string;

  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 供应商地址
   */
  supplierAddr?: string;

  /**
   * 联系人
   */
  contact?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 电子邮件
   */
  email?: string;

  /**
   * 纳税人识别号
   */
  taxPayerNumber?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface SupplierQuery extends PageQuery {
  /**
   * 供应商编号
   */
  supplierCode?: string;

  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 供应商地址
   */
  supplierAddr?: string;

  /**
   * 联系人
   */
  contact?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * 电子邮件
   */
  email?: string;

  /**
   * 纳税人识别号
   */
  taxPayerNumber?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
