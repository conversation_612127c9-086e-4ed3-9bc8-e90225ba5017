/**
 * foreachTree 函数使用示例
 */

import { foreachTree, convertChildrenField, traverseTree } from './foreachTree';

// 示例数据：包含 child 属性的树形结构
const sampleTreeData = [
  {
    id: 1,
    name: '根节点1',
    child: [
      {
        id: 2,
        name: '子节点1-1',
        child: [
          {
            id: 3,
            name: '子节点1-1-1',
            child: []
          }
        ]
      },
      {
        id: 4,
        name: '子节点1-2',
        child: []
      }
    ]
  },
  {
    id: 5,
    name: '根节点2',
    child: [
      {
        id: 6,
        name: '子节点2-1',
        child: []
      }
    ]
  }
];

// 示例1：使用 foreachTree 将 child 转换为 children，并添加图标
export function example1() {
  const data = JSON.parse(JSON.stringify(sampleTreeData)); // 深拷贝
  
  console.log('原始数据:', data);
  
  foreachTree(data, (item: any) => {
    item.icon = 'folder'; // 添加图标
    console.log(`处理节点: ${item.name}`);
  });
  
  console.log('处理后的数据:', data);
  return data;
}

// 示例2：使用 convertChildrenField 转换自定义字段名
export function example2() {
  const data = JSON.parse(JSON.stringify(sampleTreeData)); // 深拷贝
  
  console.log('原始数据:', data);
  
  convertChildrenField(data, 'child', (item: any) => {
    item.processed = true; // 标记已处理
    item.level = item.level || 0; // 添加层级信息
  });
  
  console.log('处理后的数据:', data);
  return data;
}

// 示例3：使用 traverseTree 遍历已转换的树结构
export function example3() {
  // 先转换数据
  const data = example2();
  
  console.log('开始遍历树结构:');
  
  traverseTree(data, (item: any, parent: any) => {
    const level = parent.level !== undefined ? parent.level + 1 : 0;
    item.level = level;
    item.fullPath = parent.fullPath ? `${parent.fullPath}/${item.name}` : item.name;
    
    console.log(`节点: ${item.name}, 层级: ${level}, 路径: ${item.fullPath}`);
  });
  
  console.log('遍历完成，最终数据:', data);
  return data;
}

// 示例4：处理部门树数据的实际用例
export function processDeptTreeData(deptData: any[]) {
  // 将 child 转换为 children，并添加必要的属性
  foreachTree(deptData, (item: any) => {
    // 添加图标
    item.icon = 'el:group';
    
    // 确保有 key 属性（用于树组件）
    if (!item.key && item.nodeId) {
      item.key = item.nodeId.toString();
    }
    
    // 添加标题字段（如果没有）
    if (!item.title && item.nodeName) {
      item.title = item.nodeName;
    }
    
    console.log(`处理部门节点: ${item.nodeName || item.name}`);
  });
  
  return deptData;
}

// 导出所有示例函数
export const examples = {
  example1,
  example2,
  example3,
  processDeptTreeData
};

// 如果直接运行此文件，执行所有示例
if (typeof window === 'undefined') {
  console.log('=== foreachTree 函数示例 ===');
  
  console.log('\n--- 示例1: 基本用法 ---');
  example1();
  
  console.log('\n--- 示例2: 自定义字段转换 ---');
  example2();
  
  console.log('\n--- 示例3: 树结构遍历 ---');
  example3();
}
